-- =====================================================
-- 智谱AI配置修复脚本
-- 解决智谱AI模型对话报错问题
-- =====================================================

-- 1. 检查当前智谱AI配置
SELECT '=== 当前智谱AI API密钥配置 ===' as info;
SELECT id, name, platform, api_key, url, status, create_time 
FROM ai_api_key 
WHERE platform = 'ZhiPu';

SELECT '=== 当前智谱AI模型配置 ===' as info;
SELECT id, name, model, platform, type, key_id, status, temperature, max_tokens 
FROM ai_model 
WHERE platform = 'ZhiPu';

-- 2. 修复智谱AI API密钥配置
-- 确保URL使用OpenAI兼容模式
UPDATE ai_api_key 
SET url = 'https://open.bigmodel.cn/api/paas/v4/' 
WHERE platform = 'ZhiPu' 
  AND (url IS NULL OR url = '' OR url != 'https://open.bigmodel.cn/api/paas/v4/');

-- 确保API密钥状态为启用
UPDATE ai_api_key 
SET status = 0 
WHERE platform = 'ZhiPu' 
  AND status != 0;

-- 3. 修复智谱AI模型配置
-- 修正模型名称为小写格式
UPDATE ai_model 
SET model = LOWER(model)
WHERE platform = 'ZhiPu' 
  AND model REGEXP '^[A-Z]';

-- 常见的智谱AI模型名称修正
UPDATE ai_model 
SET model = 'glm-4.5' 
WHERE platform = 'ZhiPu' 
  AND model IN ('GLM-4.5', 'GLM-4-5', 'glm-4-5');

UPDATE ai_model 
SET model = 'glm-4' 
WHERE platform = 'ZhiPu' 
  AND model IN ('GLM-4', 'GLM4');

UPDATE ai_model 
SET model = 'glm-3-turbo' 
WHERE platform = 'ZhiPu' 
  AND model IN ('GLM-3-TURBO', 'GLM-3-Turbo');

-- 确保模型状态为启用
UPDATE ai_model 
SET status = 0 
WHERE platform = 'ZhiPu' 
  AND status != 0;

-- 4. 如果没有智谱AI配置，创建默认配置
-- 插入默认API密钥（如果不存在）
INSERT IGNORE INTO ai_api_key (
    name, 
    platform, 
    api_key, 
    url, 
    status, 
    creator, 
    create_time, 
    updater, 
    update_time
) VALUES (
    '智谱AI默认', 
    'ZhiPu', 
    'your-zhipu-api-key-here', 
    'https://open.bigmodel.cn/api/paas/v4/', 
    1, -- 默认禁用，需要用户配置正确的API密钥后启用
    'system', 
    NOW(), 
    'system', 
    NOW()
);

-- 获取刚插入或已存在的智谱AI API密钥ID
SET @zhipu_key_id = (SELECT id FROM ai_api_key WHERE platform = 'ZhiPu' LIMIT 1);

-- 插入默认模型配置（如果不存在）
INSERT IGNORE INTO ai_model (
    name, 
    model, 
    platform, 
    type, 
    key_id, 
    sort, 
    status, 
    temperature, 
    max_tokens, 
    max_contexts, 
    creator, 
    create_time, 
    updater, 
    update_time
) VALUES 
(
    'GLM-4.5 智能助手', 
    'glm-4.5', 
    'ZhiPu', 
    1, -- 聊天模型
    @zhipu_key_id, 
    10, 
    1, -- 默认禁用，需要配置正确API密钥后启用
    0.7, 
    4096, 
    20, 
    'system', 
    NOW(), 
    'system', 
    NOW()
),
(
    'GLM-4 通用模型', 
    'glm-4', 
    'ZhiPu', 
    1, -- 聊天模型
    @zhipu_key_id, 
    11, 
    1, -- 默认禁用
    0.7, 
    2048, 
    15, 
    'system', 
    NOW(), 
    'system', 
    NOW()
);

-- 5. 验证修复结果
SELECT '=== 修复后的智谱AI API密钥配置 ===' as info;
SELECT id, name, platform, 
       CONCAT(LEFT(api_key, 10), '...') as api_key_preview,
       url, status, create_time 
FROM ai_api_key 
WHERE platform = 'ZhiPu';

SELECT '=== 修复后的智谱AI模型配置 ===' as info;
SELECT id, name, model, platform, type, key_id, status, temperature, max_tokens 
FROM ai_model 
WHERE platform = 'ZhiPu';

-- 6. 提示信息
SELECT '=== 修复完成提示 ===' as info;
SELECT '请注意：' as message 
UNION ALL
SELECT '1. 请将 your-zhipu-api-key-here 替换为您的真实智谱AI API密钥' 
UNION ALL
SELECT '2. 替换后请将对应的 ai_api_key 记录的 status 设置为 0（启用）' 
UNION ALL
SELECT '3. 然后将对应的 ai_model 记录的 status 设置为 0（启用）' 
UNION ALL
SELECT '4. 智谱AI API密钥格式示例：32f84543e54eee31f8d56b2bd6020573.3vh9idLJZ2ZhxDEs' 
UNION ALL
SELECT '5. 可以使用新的智能配置功能进行连接测试验证';
