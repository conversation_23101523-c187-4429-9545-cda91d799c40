-- =====================================================
-- 智谱AI URL路径修复脚本
-- 解决404 Not Found错误 - URL路径重复问题
-- =====================================================

-- 问题分析：
-- 错误URL: https://open.bigmodel.cn/api/paas/v4/v1/chat/completions
-- 正确URL: https://open.bigmodel.cn/api/paas/v4/chat/completions
-- 
-- 原因：智谱AI的OpenAI兼容接口基础URL应该是 https://open.bigmodel.cn/api/paas/v4/
-- 但是OpenAI客户端会自动添加 /v1 路径，导致路径重复

-- 1. 检查当前智谱AI配置
SELECT '=== 当前智谱AI URL配置 ===' as info;
SELECT id, name, platform, url, status 
FROM ai_api_key 
WHERE platform = 'ZhiPu';

-- 2. 修复智谱AI URL配置
-- 方案1：使用不带/v1的基础URL，让OpenAI客户端自动添加
UPDATE ai_api_key 
SET url = 'https://open.bigmodel.cn/api/paas/v4' 
WHERE platform = 'ZhiPu';

-- 3. 验证修复结果
SELECT '=== 修复后的智谱AI URL配置 ===' as info;
SELECT id, name, platform, url, status 
FROM ai_api_key 
WHERE platform = 'ZhiPu';

-- 4. 提示信息
SELECT '=== 修复说明 ===' as info;
SELECT '智谱AI URL已修复为: https://open.bigmodel.cn/api/paas/v4' as message
UNION ALL
SELECT '最终调用URL将是: https://open.bigmodel.cn/api/paas/v4/chat/completions' 
UNION ALL
SELECT '请重启应用后测试智谱AI对话功能';
