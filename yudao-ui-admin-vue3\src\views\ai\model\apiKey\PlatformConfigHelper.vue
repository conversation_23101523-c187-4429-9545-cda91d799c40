<template>
  <div class="platform-config-helper">
    <!-- 平台配置指南 -->
    <el-card v-if="selectedPlatform && platformConfig" class="config-guide-card">
      <template #header>
        <div class="card-header">
          <span>{{ platformConfig.name }} 配置指南</span>
          <el-tag :type="platformConfig.tagType">{{ platformConfig.status }}</el-tag>
        </div>
      </template>
      
      <div class="config-content">
        <!-- URL配置说明 -->
        <div class="config-section">
          <h4><el-icon><Link /></el-icon> API地址配置</h4>
          <div class="url-examples">
            <div class="example-item">
              <span class="label">推荐地址：</span>
              <el-tag class="url-tag" type="success">{{ platformConfig?.defaultUrl }}</el-tag>
              <el-button
                size="small"
                type="primary"
                link
                @click="copyToClipboard(platformConfig?.defaultUrl || '')"
              >
                复制
              </el-button>
            </div>
            <div v-if="platformConfig && platformConfig.alternativeUrls.length > 0" class="alternative-urls">
              <span class="label">备选地址：</span>
              <div class="url-list">
                <div
                  v-for="(url, index) in (platformConfig?.alternativeUrls || [])"
                  :key="index"
                  class="url-item"
                >
                  <el-tag class="url-tag" type="info">{{ url }}</el-tag>
                  <el-button 
                    size="small" 
                    type="primary" 
                    link 
                    @click="copyToClipboard(url)"
                  >
                    复制
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API密钥获取指南 -->
        <div class="config-section">
          <h4><el-icon><Key /></el-icon> API密钥获取</h4>
          <div class="steps">
            <el-steps direction="vertical" :active="(platformConfig?.steps || []).length">
              <el-step
                v-for="(step, index) in (platformConfig?.steps || [])"
                :key="index"
                :title="step.title"
                :description="step.description"
              />
            </el-steps>
          </div>
        </div>

        <!-- 官方文档链接 -->
        <div class="config-section">
          <h4><el-icon><Document /></el-icon> 相关链接</h4>
          <div class="links">
            <el-link
              v-for="(link, index) in (platformConfig?.links || [])"
              :key="index"
              :href="link.url"
              target="_blank"
              :type="link.type"
            >
              {{ link.title }}
            </el-link>
          </div>
        </div>

        <!-- 注意事项 -->
        <div v-if="platformConfig && platformConfig.notes.length > 0" class="config-section">
          <h4><el-icon><Warning /></el-icon> 注意事项</h4>
          <ul class="notes-list">
            <li v-for="(note, index) in (platformConfig?.notes || [])" :key="index">
              {{ note }}
            </li>
          </ul>
        </div>
      </div>
    </el-card>

    <!-- 快速配置按钮 -->
    <div v-if="selectedPlatform" class="quick-actions">
      <el-button 
        type="primary" 
        @click="useDefaultConfig"
      >
        使用默认配置
      </el-button>
      <el-button 
        type="success" 
        @click="testConnection"
        :loading="testing"
      >
        测试连接
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, Key, Document, Warning } from '@element-plus/icons-vue'
import { AiPlatformEnum } from '@/views/ai/utils/constants'

interface Props {
  platform?: string
}

const props = defineProps<Props>()
const emit = defineEmits(['use-default-config', 'test-connection'])

const testing = ref(false)

// 平台配置映射
const platformConfigs = {
  'OpenAI': {
    name: 'OpenAI',
    status: '稳定',
    tagType: 'success',
    defaultUrl: 'https://api.openai.com/v1',
    alternativeUrls: [
      'https://api.gptsapi.net/v1',
      'https://api.chatanywhere.tech/v1'
    ],
    steps: [
      { title: '注册OpenAI账号', description: '访问 https://platform.openai.com 注册账号' },
      { title: '创建API密钥', description: '在API Keys页面创建新的密钥' },
      { title: '复制密钥', description: '复制生成的sk-开头的密钥' }
    ],
    links: [
      { title: '官方文档', url: 'https://platform.openai.com/docs', type: 'primary' },
      { title: 'API参考', url: 'https://platform.openai.com/docs/api-reference', type: 'info' }
    ],
    notes: [
      '需要海外信用卡进行验证',
      '国内访问可能需要代理',
      '建议使用国内中转服务'
    ]
  },
  'ZhiPu': {
    name: '智谱AI',
    status: '推荐',
    tagType: 'success',
    defaultUrl: 'https://open.bigmodel.cn/api/paas/v4',
    alternativeUrls: [],
    steps: [
      { title: '注册智谱AI账号', description: '访问 https://open.bigmodel.cn 注册账号' },
      { title: '实名认证', description: '完成个人或企业实名认证' },
      { title: '创建API密钥', description: '在控制台创建API密钥' }
    ],
    links: [
      { title: '官方网站', url: 'https://open.bigmodel.cn', type: 'primary' },
      { title: '开发文档', url: 'https://open.bigmodel.cn/dev/api', type: 'info' }
    ],
    notes: [
      '国内平台，访问稳定',
      '支持OpenAI兼容格式',
      '新用户有免费额度'
    ]
  },
  'DeepSeek': {
    name: 'DeepSeek',
    status: '推荐',
    tagType: 'success',
    defaultUrl: 'https://api.deepseek.com/v1',
    alternativeUrls: [],
    steps: [
      { title: '注册DeepSeek账号', description: '访问 https://platform.deepseek.com 注册' },
      { title: '获取API密钥', description: '在API Keys页面创建密钥' }
    ],
    links: [
      { title: '官方平台', url: 'https://platform.deepseek.com', type: 'primary' },
      { title: 'API文档', url: 'https://platform.deepseek.com/api-docs', type: 'info' }
    ],
    notes: [
      '国内访问友好',
      '价格相对便宜',
      '支持长上下文'
    ]
  },
  'HunYuan': {
    name: '腾讯混元',
    status: '稳定',
    tagType: 'success',
    defaultUrl: 'https://api.hunyuan.cloud.tencent.com',
    alternativeUrls: [],
    steps: [
      { title: '开通腾讯云账号', description: '注册并实名认证腾讯云账号' },
      { title: '开通混元服务', description: '在腾讯云控制台开通混元大模型服务' },
      { title: '创建密钥', description: '创建API密钥用于调用' }
    ],
    links: [
      { title: '腾讯云控制台', url: 'https://console.cloud.tencent.com', type: 'primary' },
      { title: '混元文档', url: 'https://cloud.tencent.com/document/product/1729', type: 'info' }
    ],
    notes: [
      '需要腾讯云账号',
      '按量计费',
      '国内访问稳定'
    ]
  },
  'TongYi': {
    name: '通义千问',
    status: '稳定',
    tagType: 'success',
    defaultUrl: 'https://dashscope.aliyuncs.com/api/v1',
    alternativeUrls: [],
    steps: [
      { title: '注册阿里云账号', description: '访问 https://www.aliyun.com 注册账号' },
      { title: '开通灵积服务', description: '在阿里云控制台开通灵积模型服务' },
      { title: '创建API密钥', description: '获取API Key用于调用' }
    ],
    links: [
      { title: '阿里云控制台', url: 'https://dashscope.console.aliyun.com', type: 'primary' },
      { title: '开发文档', url: 'https://help.aliyun.com/zh/dashscope', type: 'info' }
    ],
    notes: [
      '需要阿里云账号',
      '国内访问稳定',
      '新用户有免费额度'
    ]
  },
  'YiYan': {
    name: '文心一言',
    status: '稳定',
    tagType: 'success',
    defaultUrl: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1',
    alternativeUrls: [],
    steps: [
      { title: '注册百度账号', description: '访问 https://cloud.baidu.com 注册账号' },
      { title: '创建应用', description: '在千帆大模型平台创建应用' },
      { title: '获取密钥', description: '获取API Key和Secret Key' }
    ],
    links: [
      { title: '千帆平台', url: 'https://cloud.baidu.com/product/wenxinworkshop', type: 'primary' },
      { title: '开发文档', url: 'https://cloud.baidu.com/doc/WENXINWORKSHOP', type: 'info' }
    ],
    notes: [
      '需要百度云账号',
      '使用AppKey|SecretKey格式',
      '国内访问稳定'
    ]
  },
  'Gemini': {
    name: 'Google Gemini',
    status: '需代理',
    tagType: 'warning',
    defaultUrl: 'https://generativelanguage.googleapis.com',
    alternativeUrls: [
      'https://api.chatanywhere.tech/v1',
      'https://gemini-api.apifox.cn'
    ],
    steps: [
      { title: '注册Google账号', description: '访问 https://ai.google.dev 注册' },
      { title: '获取API密钥', description: '在Google AI Studio创建API密钥' },
      { title: '配置代理', description: '国内需要配置代理或使用中转服务' }
    ],
    links: [
      { title: 'Google AI Studio', url: 'https://ai.google.dev', type: 'primary' },
      { title: 'API文档', url: 'https://ai.google.dev/docs', type: 'info' }
    ],
    notes: [
      '国内访问需要代理',
      '建议使用中转服务',
      '支持多模态输入'
    ]
  },
  'SiliconFlow': {
    name: '硅基流动',
    status: '推荐',
    tagType: 'success',
    defaultUrl: 'https://api.siliconflow.cn',
    alternativeUrls: [],
    steps: [
      { title: '注册硅基流动账号', description: '访问 https://siliconflow.cn 注册账号' },
      { title: '获取API密钥', description: '在控制台创建API密钥' }
    ],
    links: [
      { title: '官方网站', url: 'https://siliconflow.cn', type: 'primary' },
      { title: 'API文档', url: 'https://docs.siliconflow.cn', type: 'info' }
    ],
    notes: [
      '国内访问稳定',
      '支持多种模型',
      '价格相对便宜'
    ]
  },
  'XingHuo': {
    name: '讯飞星火',
    status: '稳定',
    tagType: 'success',
    defaultUrl: 'https://spark-api-open.xf-yun.com/v1',
    alternativeUrls: [],
    steps: [
      { title: '注册讯飞账号', description: '访问 https://www.xfyun.cn 注册账号' },
      { title: '创建应用', description: '在讯飞开放平台创建应用' },
      { title: '获取密钥', description: '获取APPID、APISecret和APIKey' }
    ],
    links: [
      { title: '讯飞开放平台', url: 'https://www.xfyun.cn', type: 'primary' },
      { title: 'API文档', url: 'https://www.xfyun.cn/doc/spark/Web.html', type: 'info' }
    ],
    notes: [
      '需要讯飞开发者账号',
      '使用APPID|APISecret|APIKey格式',
      '国内访问稳定'
    ]
  }
}

const selectedPlatform = computed(() => props.platform)
const platformConfig = computed(() => {
  if (!selectedPlatform.value) return null
  const config = platformConfigs[selectedPlatform.value]
  if (!config) {
    // 返回默认配置
    return {
      name: selectedPlatform.value,
      status: '未配置',
      tagType: 'info',
      defaultUrl: '',
      alternativeUrls: [],
      steps: [
        { title: '查看官方文档', description: '请查看该平台的官方文档获取配置信息' }
      ],
      links: [],
      notes: ['该平台暂未提供配置指南，请参考官方文档']
    }
  }
  return config
})

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败')
  }
}

const useDefaultConfig = () => {
  if (platformConfig.value) {
    emit('use-default-config', {
      url: platformConfig.value.defaultUrl,
      platform: selectedPlatform.value
    })
  }
}

const testConnection = async () => {
  testing.value = true
  try {
    emit('test-connection')
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.platform-config-helper {
  margin-top: 16px;
}

.config-guide-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-content {
  max-height: 400px;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
}

.config-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #409eff;
}

.url-examples {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

.example-item,
.url-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.label {
  font-weight: 500;
  min-width: 80px;
}

.url-tag {
  font-family: monospace;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.alternative-urls {
  margin-top: 12px;
}

.url-list {
  margin-left: 80px;
}

.steps {
  margin-left: 16px;
}

.links {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.notes-list {
  margin: 0;
  padding-left: 20px;
}

.notes-list li {
  margin-bottom: 8px;
  color: #e6a23c;
}

.quick-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
