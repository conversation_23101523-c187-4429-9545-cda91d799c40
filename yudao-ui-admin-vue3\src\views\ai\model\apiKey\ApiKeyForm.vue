<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="所属平台" prop="platform">
        <el-select
          v-model="formData.platform"
          placeholder="请选择平台"
          clearable
          @change="onPlatformChange"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.AI_PLATFORM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>

      <el-form-item label="密钥" prop="apiKey">
        <el-input
          v-model="formData.apiKey"
          placeholder="请输入密钥"
          type="password"
          show-password
        />
      </el-form-item>

      <el-form-item label="API地址" prop="url">
        <div class="url-input-container">
          <el-input
            v-model="formData.url"
            placeholder="请输入API地址（留空使用默认地址）"
            @blur="onUrlBlur"
          />
          <div class="url-actions">
            <el-button
              size="small"
              type="primary"
              link
              @click="useDefaultConfig"
              :disabled="!formData.platform"
            >
              使用默认
            </el-button>
            <el-button
              size="small"
              type="success"
              link
              @click="testConnection"
              :loading="testing"
              :disabled="!canTest"
            >
              测试连接
            </el-button>
          </div>
        </div>
        <!-- URL验证提示 -->
        <div v-if="urlValidationMessage" class="url-validation">
          <el-alert
            :title="urlValidationMessage"
            :type="urlValidationType"
            :closable="false"
            show-icon
          />
        </div>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 平台配置指南 -->
    <PlatformConfigHelper
      :platform="formData.platform"
      @use-default-config="onUseDefaultConfig"
      @test-connection="testConnection"
    />

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { getIntDictOptions, DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { ApiKeyApi, ApiKeyVO } from '@/api/ai/model/apiKey'
import { CommonStatusEnum } from '@/utils/constants'
import PlatformConfigHelper from './PlatformConfigHelper.vue'

/** AI API 密钥 表单 */
defineOptions({ name: 'ApiKeyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  apiKey: undefined,
  platform: undefined,
  url: undefined,
  status: CommonStatusEnum.ENABLE
})
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  apiKey: [{ required: true, message: '密钥不能为空', trigger: 'blur' }],
  platform: [{ required: true, message: '平台不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

// 新增状态变量
const testing = ref(false) // 连接测试状态
const urlValidationMessage = ref('') // URL验证消息
const urlValidationType = ref<'success' | 'warning' | 'danger'>('success') // 验证消息类型

// 计算属性
const canTest = computed(() => {
  return formData.value.platform && formData.value.apiKey && !testing.value
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ApiKeyApi.getApiKey(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ApiKeyVO
    if (formType.value === 'create') {
      await ApiKeyApi.createApiKey(data)
      message.success(t('common.createSuccess'))
    } else {
      await ApiKeyApi.updateApiKey(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    apiKey: undefined,
    platform: undefined,
    url: undefined,
    status: CommonStatusEnum.ENABLE
  }
  formRef.value?.resetFields()
  // 清空验证消息
  urlValidationMessage.value = ''
  urlValidationType.value = 'success'
}

/** 平台变更处理 */
const onPlatformChange = (platform: string) => {
  if (platform) {
    // 清空URL，让用户选择使用默认配置
    if (!formData.value.url) {
      urlValidationMessage.value = '建议点击"使用默认"按钮自动配置推荐的API地址'
      urlValidationType.value = 'warning'
    }

    // 如果名称为空，自动填充平台名称
    if (!formData.value.name) {
      const platformDict = getStrDictOptions(DICT_TYPE.AI_PLATFORM).find(dict => dict.value === platform)
      if (platformDict) {
        formData.value.name = platformDict.label + ' API'
      }
    }
  } else {
    urlValidationMessage.value = ''
  }
}

/** URL失焦处理 */
const onUrlBlur = async () => {
  if (formData.value.url && formData.value.platform) {
    try {
      // 调用后端验证和修正URL
      const result = await ApiKeyApi.validateConfig(formData.value as ApiKeyVO)
      if (result.url !== formData.value.url) {
        formData.value.url = result.url
        urlValidationMessage.value = 'URL已自动修正为标准格式'
        urlValidationType.value = 'success'
      } else {
        urlValidationMessage.value = 'URL格式验证通过'
        urlValidationType.value = 'success'
      }
    } catch (error) {
      urlValidationMessage.value = 'URL格式验证失败：' + error.message
      urlValidationType.value = 'danger'
    }
  }
}

/** 使用默认配置 */
const useDefaultConfig = () => {
  // 这个方法由PlatformConfigHelper组件触发
}

/** 处理使用默认配置事件 */
const onUseDefaultConfig = (config: { url: string; platform: string }) => {
  if (config.url) {
    formData.value.url = config.url
    urlValidationMessage.value = '已使用推荐的默认API地址'
    urlValidationType.value = 'success'
  }
}

/** 测试连接 */
const testConnection = async () => {
  if (!formData.value.platform || !formData.value.apiKey) {
    message.warning('请先选择平台并输入API密钥')
    return
  }

  testing.value = true
  try {
    const result = await ApiKeyApi.testConnection({
      platform: formData.value.platform,
      apiKey: formData.value.apiKey,
      url: formData.value.url
    })

    if (result.includes('成功')) {
      message.success(result)
      urlValidationMessage.value = result
      urlValidationType.value = 'success'
    } else {
      message.warning(result)
      urlValidationMessage.value = result
      urlValidationType.value = 'warning'
    }
  } catch (error) {
    const errorMsg = '连接测试失败：' + error.message
    message.error(errorMsg)
    urlValidationMessage.value = errorMsg
    urlValidationType.value = 'danger'
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.url-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.url-input-container .el-input {
  flex: 1;
}

.url-actions {
  display: flex;
  gap: 4px;
  white-space: nowrap;
}

.url-validation {
  margin-top: 8px;
}

.url-validation .el-alert {
  padding: 8px 12px;
}

.url-validation .el-alert__title {
  font-size: 12px;
  line-height: 1.4;
}
</style>
