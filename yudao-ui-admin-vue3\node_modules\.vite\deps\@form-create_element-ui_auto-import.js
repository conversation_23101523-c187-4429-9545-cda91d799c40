import "./chunk-GUNWUUO7.js";
import "./chunk-4O5CUXTJ.js";
import "./chunk-Z5QI6ANV.js";
import "./chunk-CWA4IGAI.js";
import "./chunk-6B3ILNFL.js";
import "./chunk-QJ3D3FHZ.js";
import "./chunk-Y3LXBSAC.js";
import "./chunk-MAVRPME3.js";
import "./chunk-DF4OBUBK.js";
import "./chunk-HPI3FC5N.js";
import "./chunk-5C5PLGDE.js";
import "./chunk-H7OZVV2J.js";
import "./chunk-C35XOVUI.js";
import "./chunk-TGJ67SFD.js";
import "./chunk-OCHQSMSJ.js";
import "./chunk-WTH5R5RP.js";
import "./chunk-QWPROHCH.js";
import "./chunk-O52VNINV.js";
import "./chunk-ETC7WPR7.js";
import "./chunk-4332WDED.js";
import "./chunk-GDBABZ6T.js";
import "./chunk-YZ2N54P2.js";
import "./chunk-6I3BJQLL.js";
import "./chunk-U45BN6WK.js";
import "./chunk-DPZ5MXIL.js";
import "./chunk-JGMSOSXL.js";
import "./chunk-S2CIUJWX.js";
import "./chunk-2M65ATGS.js";
import "./chunk-HTWHUAAI.js";
import "./chunk-2NNXEQ5E.js";
import "./chunk-F2VYWOKL.js";
import "./chunk-7U7XU277.js";
import "./chunk-Q3DHEXTN.js";
import {
  ElAutocomplete,
  ElButton,
  ElCascader,
  ElCheckbox,
  ElCheckboxButton,
  ElCheckboxGroup,
  ElCol,
  ElColorPicker,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElIcon,
  ElInput,
  ElInputNumber,
  ElPopover,
  ElProgress,
  ElRadio,
  ElRadioButton,
  ElRadioGroup,
  ElRate,
  ElRow,
  ElSelect,
  ElSlider,
  ElSwitch,
  ElTimePicker,
  ElTooltip,
  ElTree,
  ElUpload
} from "./chunk-DUJBKLU4.js";
import "./chunk-GVKQVKU2.js";
import "./chunk-TQUTZUXW.js";
import "./chunk-PHKUHJQP.js";
import "./chunk-O7KFMITO.js";
import "./chunk-LROEKXT5.js";
import "./chunk-YNRHTVZR.js";
import "./chunk-7BPWZNUD.js";
import "./chunk-OBYNDG2A.js";
import "./chunk-67TUTJCN.js";
import "./chunk-ULX5FOVL.js";
import "./chunk-GTWINWNV.js";
import "./chunk-GFT2G5UO.js";

// node_modules/.pnpm/@form-create+element-ui@3.2.14_vue@3.5.12_typescript@5.3.3_/node_modules/@form-create/element-ui/auto-import.js
function install(formCreate) {
  formCreate.useApp((_, app) => {
    app.component(ElForm.name) || app.use(ElForm);
    app.component(ElButton.name) || app.use(ElButton);
    app.component(ElRow.name) || app.use(ElRow);
    app.component(ElCol.name) || app.use(ElCol);
    app.component(ElInput.name) || app.use(ElInput);
    app.component(ElInputNumber.name) || app.use(ElInputNumber);
    app.component(ElCascader.name) || app.use(ElCascader);
    app.component(ElPopover.name) || app.use(ElPopover);
    app.component(ElTooltip.name) || app.use(ElTooltip);
    app.component(ElAutocomplete.name) || app.use(ElAutocomplete);
    app.component(ElCheckboxGroup.name) || app.use(ElCheckboxGroup);
    app.component(ElCheckboxButton.name) || app.use(ElCheckboxButton);
    app.component(ElRadioGroup.name) || app.use(ElRadioGroup);
    app.component(ElRadioButton.name) || app.use(ElRadioButton);
    app.component(ElRadio.name) || app.use(ElRadio);
    app.component(ElDialog.name) || app.use(ElDialog);
    app.component(ElCheckbox.name) || app.use(ElCheckbox);
    app.component(ElSelect.name) || app.use(ElSelect);
    app.component(ElTree.name) || app.use(ElTree);
    app.component(ElUpload.name) || app.use(ElUpload);
    app.component(ElSlider.name) || app.use(ElSlider);
    app.component(ElRate.name) || app.use(ElRate);
    app.component(ElColorPicker.name) || app.use(ElColorPicker);
    app.component(ElSwitch.name) || app.use(ElSwitch);
    app.component(ElDatePicker.name) || app.use(ElDatePicker);
    app.component(ElIcon.name) || app.use(ElIcon);
    app.component(ElTimePicker.name) || app.use(ElTimePicker);
    app.component(ElProgress.name) || app.use(ElProgress);
  });
}
export {
  install as default
};
//# sourceMappingURL=@form-create_element-ui_auto-import.js.map
