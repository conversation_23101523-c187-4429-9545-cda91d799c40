package cn.iocoder.yudao.module.ai.framework.ai.core;

import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * AI平台URL智能处理器
 * 
 * 提供URL智能检测、修正和优化功能，解决各个AI平台URL配置问题
 * 
 * <AUTHOR>
 */
@Slf4j
public final class AiUrlSmartProcessor {

    // ========== URL修正规则映射 ==========
    
    private static final Map<AiPlatformEnum, Function<String, String>> URL_CORRECTION_RULES = new HashMap<>();
    
    static {
        // OpenAI URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.OPENAI, AiUrlSmartProcessor::correctOpenAiUrl);
        
        // 智谱AI URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.ZHI_PU, AiUrlSmartProcessor::correctZhiPuUrl);
        
        // DeepSeek URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.DEEP_SEEK, AiUrlSmartProcessor::correctDeepSeekUrl);
        
        // 腾讯混元 URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.HUN_YUAN, AiUrlSmartProcessor::correctHunYuanUrl);
        
        // 通义千问 URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.TONG_YI, AiUrlSmartProcessor::correctTongYiUrl);
        
        // 文心一言 URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.YI_YAN, AiUrlSmartProcessor::correctYiYanUrl);
        
        // 字节豆包 URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.DOU_BAO, AiUrlSmartProcessor::correctDouBaoUrl);
        
        // 硅基流动 URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.SILICON_FLOW, AiUrlSmartProcessor::correctSiliconFlowUrl);
        
        // Google Gemini URL修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.GEMINI, AiUrlSmartProcessor::correctGeminiUrl);
        
        // 其他平台使用通用修正规则
        URL_CORRECTION_RULES.put(AiPlatformEnum.XING_HUO, AiUrlSmartProcessor::correctGenericUrl);
        URL_CORRECTION_RULES.put(AiPlatformEnum.MINI_MAX, AiUrlSmartProcessor::correctGenericUrl);
        URL_CORRECTION_RULES.put(AiPlatformEnum.MOONSHOT, AiUrlSmartProcessor::correctGenericUrl);
        URL_CORRECTION_RULES.put(AiPlatformEnum.BAI_CHUAN, AiUrlSmartProcessor::correctGenericUrl);
        URL_CORRECTION_RULES.put(AiPlatformEnum.AZURE_OPENAI, AiUrlSmartProcessor::correctGenericUrl);
        URL_CORRECTION_RULES.put(AiPlatformEnum.OLLAMA, AiUrlSmartProcessor::correctGenericUrl);
        URL_CORRECTION_RULES.put(AiPlatformEnum.STABLE_DIFFUSION, AiUrlSmartProcessor::correctGenericUrl);
        URL_CORRECTION_RULES.put(AiPlatformEnum.MIDJOURNEY, AiUrlSmartProcessor::correctGenericUrl);
        URL_CORRECTION_RULES.put(AiPlatformEnum.SUNO, AiUrlSmartProcessor::correctGenericUrl);
    }

    /**
     * 智能处理和修正URL
     * 
     * @param platform AI平台枚举
     * @param inputUrl 用户输入的URL
     * @return 修正后的URL
     */
    public static String smartProcessUrl(AiPlatformEnum platform, String inputUrl) {
        // 如果输入为空，返回默认URL
        if (StrUtil.isBlank(inputUrl)) {
            String defaultUrl = AiPlatformUrlConstants.getDefaultUrl(platform);
            log.info("使用平台 {} 的默认URL: {}", platform.getName(), defaultUrl);
            return defaultUrl;
        }

        // 获取对应平台的修正规则
        Function<String, String> correctionRule = URL_CORRECTION_RULES.get(platform);
        if (correctionRule == null) {
            log.warn("平台 {} 没有对应的URL修正规则，使用通用修正", platform.getName());
            correctionRule = AiUrlSmartProcessor::correctGenericUrl;
        }

        // 应用修正规则
        String correctedUrl = correctionRule.apply(inputUrl.trim());
        
        if (!inputUrl.trim().equals(correctedUrl)) {
            log.info("平台 {} URL已修正: {} -> {}", platform.getName(), inputUrl.trim(), correctedUrl);
        }
        
        return correctedUrl;
    }

    /**
     * 智能处理和修正URL（字符串平台参数）
     * 
     * @param platformStr 平台字符串
     * @param inputUrl 用户输入的URL
     * @return 修正后的URL
     */
    public static String smartProcessUrl(String platformStr, String inputUrl) {
        try {
            AiPlatformEnum platform = AiPlatformEnum.validatePlatform(platformStr);
            return smartProcessUrl(platform, inputUrl);
        } catch (IllegalArgumentException e) {
            log.warn("未知平台: {}, 使用通用URL修正", platformStr);
            return correctGenericUrl(inputUrl);
        }
    }

    // ========== 各平台具体的URL修正规则 ==========

    /**
     * OpenAI URL修正规则
     */
    private static String correctOpenAiUrl(String url) {
        url = correctGenericUrl(url);
        
        // 确保以/v1结尾
        if (!url.endsWith("/v1") && !url.endsWith("/v1/")) {
            url = url.endsWith("/") ? url + "v1" : url + "/v1";
        }
        
        return url;
    }

    /**
     * 智谱AI URL修正规则
     */
    private static String correctZhiPuUrl(String url) {
        url = correctGenericUrl(url);

        // 智谱AI使用OpenAI兼容模式，确保使用正确的端点
        if (url.contains("bigmodel.cn")) {
            // 移除可能的错误路径和多余的斜杠
            url = url.replaceAll("/api/.*$", "");
            url = url.replaceAll("/$", ""); // 移除末尾斜杠
            url = url + "/api/paas/v4"; // 不添加末尾斜杠，让OpenAI客户端自动处理
        }

        return url;
    }

    /**
     * DeepSeek URL修正规则
     */
    private static String correctDeepSeekUrl(String url) {
        url = correctGenericUrl(url);
        
        // 确保以/v1结尾
        if (!url.endsWith("/v1") && !url.endsWith("/v1/")) {
            url = url.endsWith("/") ? url + "v1" : url + "/v1";
        }
        
        return url;
    }

    /**
     * 腾讯混元 URL修正规则
     */
    private static String correctHunYuanUrl(String url) {
        url = correctGenericUrl(url);
        
        // 移除可能重复的/v1路径，因为OpenAI API会自动添加
        if (url.endsWith("/v1") || url.endsWith("/v1/")) {
            url = url.replaceAll("/v1/?$", "");
        }
        
        return url;
    }

    /**
     * 通义千问 URL修正规则
     */
    private static String correctTongYiUrl(String url) {
        url = correctGenericUrl(url);
        
        // 确保使用正确的API端点
        if (url.contains("dashscope.aliyuncs.com") && !url.contains("/api/v1")) {
            url = url.replaceAll("/api/.*$", "");
            url = url.endsWith("/") ? url + "api/v1" : url + "/api/v1";
        }
        
        return url;
    }

    /**
     * 文心一言 URL修正规则
     */
    private static String correctYiYanUrl(String url) {
        url = correctGenericUrl(url);
        
        // 确保使用正确的RPC端点
        if (url.contains("baidubce.com") && !url.contains("/rpc/2.0")) {
            url = url.replaceAll("/rpc/.*$", "");
            url = url.endsWith("/") ? url + "rpc/2.0/ai_custom/v1" : url + "/rpc/2.0/ai_custom/v1";
        }
        
        return url;
    }

    /**
     * 字节豆包 URL修正规则
     */
    private static String correctDouBaoUrl(String url) {
        url = correctGenericUrl(url);
        
        // 确保使用正确的API端点
        if (url.contains("volces.com") && !url.contains("/api")) {
            url = url.endsWith("/") ? url + "api" : url + "/api";
        }
        
        return url;
    }

    /**
     * 硅基流动 URL修正规则
     */
    private static String correctSiliconFlowUrl(String url) {
        return correctGenericUrl(url);
    }

    /**
     * Google Gemini URL修正规则
     */
    private static String correctGeminiUrl(String url) {
        url = correctGenericUrl(url);
        
        // 处理不同的Gemini服务提供商
        if (url.contains("chatanywhere.tech") || url.contains("laozhang.ai")) {
            // API中转服务兼容OpenAI格式，需要添加/v1路径
            if (!url.endsWith("/v1") && !url.endsWith("/v1/")) {
                url = url.endsWith("/") ? url + "v1" : url + "/v1";
            }
        } else if (url.contains("googleapis.com")) {
            // Google官方API，移除可能的错误路径
            url = url.replaceAll("/v1.*$", "");
        }
        
        return url;
    }

    /**
     * 通用URL修正规则
     */
    private static String correctGenericUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return url;
        }
        
        url = url.trim();
        
        // 补全协议头
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
        }
        
        // 移除末尾多余的斜杠
        while (url.endsWith("//")) {
            url = url.substring(0, url.length() - 1);
        }
        
        return url;
    }

    /**
     * 私有构造函数，防止实例化
     */
    private AiUrlSmartProcessor() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
