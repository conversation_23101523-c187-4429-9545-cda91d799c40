-- =====================================================
-- 智谱AI 404错误最终修复脚本
-- 解决URL路径重复问题：/api/paas/v4/v1/chat/completions
-- =====================================================

-- 问题分析：
-- 错误：404 Not Found from POST https://open.bigmodel.cn/api/paas/v4/v1/chat/completions
-- 原因：URL配置为 https://open.bigmodel.cn/api/paas/v4/ (带末尾斜杠)
-- OpenAI客户端自动添加 /v1/chat/completions
-- 结果：https://open.bigmodel.cn/api/paas/v4/v1/chat/completions (路径重复)
-- 
-- 正确配置：https://open.bigmodel.cn/api/paas/v4 (不带末尾斜杠)
-- 最终URL：https://open.bigmodel.cn/api/paas/v4/chat/completions

-- 1. 检查当前配置
SELECT '=== 修复前的智谱AI配置 ===' as info;
SELECT id, name, platform, url, status 
FROM ai_api_key 
WHERE platform = 'ZhiPu';

-- 2. 修复智谱AI URL配置 - 移除末尾斜杠
UPDATE ai_api_key 
SET url = 'https://open.bigmodel.cn/api/paas/v4' 
WHERE platform = 'ZhiPu';

-- 3. 确保API密钥状态正确
UPDATE ai_api_key 
SET status = 0 
WHERE platform = 'ZhiPu' 
  AND api_key != 'your-zhipu-api-key-here' 
  AND api_key IS NOT NULL 
  AND api_key != '';

-- 4. 确保模型配置正确
UPDATE ai_model 
SET model = CASE 
    WHEN model = 'GLM-4.5' THEN 'glm-4.5'
    WHEN model = 'GLM-4' THEN 'glm-4'
    WHEN model = 'GLM-3-TURBO' THEN 'glm-3-turbo'
    ELSE LOWER(model)
END,
status = 0
WHERE platform = 'ZhiPu';

-- 5. 验证修复结果
SELECT '=== 修复后的智谱AI配置 ===' as info;
SELECT id, name, platform, url, status 
FROM ai_api_key 
WHERE platform = 'ZhiPu';

SELECT '=== 智谱AI模型配置 ===' as info;
SELECT id, name, model, platform, status 
FROM ai_model 
WHERE platform = 'ZhiPu';

-- 6. 修复说明
SELECT '=== 修复说明 ===' as info;
SELECT '1. 智谱AI URL已修复为: https://open.bigmodel.cn/api/paas/v4' as message
UNION ALL
SELECT '2. 最终API调用URL: https://open.bigmodel.cn/api/paas/v4/chat/completions'
UNION ALL
SELECT '3. 模型名称已修正为小写格式'
UNION ALL
SELECT '4. 请重启应用后测试智谱AI对话功能'
UNION ALL
SELECT '5. 如果仍有问题，请检查API密钥是否有效';
