package cn.iocoder.yudao.module.ai.framework.ai.core;

import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * AI平台URL配置常量类
 * 
 * 提供各个AI平台的默认URL配置、URL格式验证和智能修正功能
 * 
 * <AUTHOR>
 */
public final class AiPlatformUrlConstants {

    // ========== 各平台默认URL配置 ==========
    
    /** OpenAI 默认URL */
    public static final String OPENAI_DEFAULT_URL = "https://api.openai.com/v1";
    
    /** 智谱AI 默认URL (OpenAI兼容模式) */
    public static final String ZHI_PU_DEFAULT_URL = "https://open.bigmodel.cn/api/paas/v4";
    
    /** DeepSeek 默认URL */
    public static final String DEEP_SEEK_DEFAULT_URL = "https://api.deepseek.com/v1";
    
    /** 腾讯混元 默认URL */
    public static final String HUN_YUAN_DEFAULT_URL = "https://api.hunyuan.cloud.tencent.com";
    
    /** 通义千问 默认URL */
    public static final String TONG_YI_DEFAULT_URL = "https://dashscope.aliyuncs.com/api/v1";
    
    /** 文心一言 默认URL */
    public static final String YI_YAN_DEFAULT_URL = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1";
    
    /** 字节豆包 默认URL */
    public static final String DOU_BAO_DEFAULT_URL = "https://ark.cn-beijing.volces.com/api";
    
    /** 硅基流动 默认URL */
    public static final String SILICON_FLOW_DEFAULT_URL = "https://api.siliconflow.cn";
    
    /** 讯飞星火 默认URL */
    public static final String XING_HUO_DEFAULT_URL = "https://spark-api-open.xf-yun.com/v1";
    
    /** MiniMax 默认URL */
    public static final String MINI_MAX_DEFAULT_URL = "https://api.minimax.chat/v1";
    
    /** 月之暗面 默认URL */
    public static final String MOONSHOT_DEFAULT_URL = "https://api.moonshot.cn/v1";
    
    /** 百川智能 默认URL */
    public static final String BAI_CHUAN_DEFAULT_URL = "https://api.baichuan-ai.com/v1";
    
    /** Google Gemini 默认URL */
    public static final String GEMINI_DEFAULT_URL = "https://generativelanguage.googleapis.com";
    
    /** Azure OpenAI 默认URL模板 */
    public static final String AZURE_OPENAI_DEFAULT_URL = "https://{resource}.openai.azure.com";
    
    /** Ollama 默认URL */
    public static final String OLLAMA_DEFAULT_URL = "http://127.0.0.1:11434";
    
    /** Stability AI 默认URL */
    public static final String STABLE_DIFFUSION_DEFAULT_URL = "https://api.stability.ai";
    
    /** Midjourney 默认URL */
    public static final String MIDJOURNEY_DEFAULT_URL = "https://api.holdai.top/mj";
    
    /** Suno AI 默认URL */
    public static final String SUNO_DEFAULT_URL = "http://127.0.0.1:3001";

    // ========== URL格式验证正则表达式 ==========
    
    /** HTTP/HTTPS URL格式验证 */
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^https?://[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?([.][a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*([:]\\d{1,5})?(/.*)?$"
    );
    
    /** API Key格式验证 */
    private static final Pattern API_KEY_PATTERN = Pattern.compile("^[a-zA-Z0-9._-]+$");

    // ========== 平台默认URL映射 ==========
    
    private static final Map<AiPlatformEnum, String> PLATFORM_DEFAULT_URLS = new HashMap<>();
    
    static {
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.OPENAI, OPENAI_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.ZHI_PU, ZHI_PU_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.DEEP_SEEK, DEEP_SEEK_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.HUN_YUAN, HUN_YUAN_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.TONG_YI, TONG_YI_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.YI_YAN, YI_YAN_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.DOU_BAO, DOU_BAO_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.SILICON_FLOW, SILICON_FLOW_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.XING_HUO, XING_HUO_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.MINI_MAX, MINI_MAX_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.MOONSHOT, MOONSHOT_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.BAI_CHUAN, BAI_CHUAN_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.GEMINI, GEMINI_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.AZURE_OPENAI, AZURE_OPENAI_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.OLLAMA, OLLAMA_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.STABLE_DIFFUSION, STABLE_DIFFUSION_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.MIDJOURNEY, MIDJOURNEY_DEFAULT_URL);
        PLATFORM_DEFAULT_URLS.put(AiPlatformEnum.SUNO, SUNO_DEFAULT_URL);
    }

    /**
     * 获取平台默认URL
     * 
     * @param platform AI平台枚举
     * @return 默认URL，如果平台不存在则返回null
     */
    public static String getDefaultUrl(AiPlatformEnum platform) {
        return PLATFORM_DEFAULT_URLS.get(platform);
    }

    /**
     * 获取平台默认URL
     * 
     * @param platformStr 平台字符串
     * @return 默认URL，如果平台不存在则返回null
     */
    public static String getDefaultUrl(String platformStr) {
        try {
            AiPlatformEnum platform = AiPlatformEnum.validatePlatform(platformStr);
            return getDefaultUrl(platform);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 验证URL格式是否正确
     * 
     * @param url 待验证的URL
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return false;
        }
        return URL_PATTERN.matcher(url.trim()).matches();
    }

    /**
     * 验证API Key格式是否正确
     * 
     * @param apiKey 待验证的API Key
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidApiKey(String apiKey) {
        if (StrUtil.isBlank(apiKey)) {
            return false;
        }
        // API Key长度通常在10-200字符之间
        String trimmedKey = apiKey.trim();
        return trimmedKey.length() >= 10 && trimmedKey.length() <= 200 && API_KEY_PATTERN.matcher(trimmedKey).matches();
    }

    /**
     * 私有构造函数，防止实例化
     */
    private AiPlatformUrlConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
