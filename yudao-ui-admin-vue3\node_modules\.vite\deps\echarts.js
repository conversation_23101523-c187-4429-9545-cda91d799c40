import {
  installUniversalTransition
} from "./chunk-AR5ZO5IW.js";
import {
  install as install49
} from "./chunk-NSSU2UD4.js";
import {
  install as install50
} from "./chunk-2A7SHRFG.js";
import {
  extendChartView,
  extendComponentModel,
  extendComponentView,
  extendSeriesModel,
  format_exports,
  graphic_exports,
  helper_exports,
  number_exports,
  time_exports,
  util_exports as util_exports2
} from "./chunk-26G46X43.js";
import {
  installLabelLayout
} from "./chunk-TAHELCKR.js";
import {
  install,
  install10 as install11,
  install11 as install12,
  install12 as install14,
  install13 as install15,
  install14 as install16,
  install15 as install17,
  install16 as install18,
  install17 as install19,
  install18 as install20,
  install19 as install21,
  install2,
  install20 as install22,
  install21 as install23,
  install22 as install24,
  install3,
  install4,
  install5,
  install6 as install7,
  install7 as install8,
  install8 as install9,
  install9 as install10
} from "./chunk-3OJOHPZ3.js";
import "./chunk-EB5BXDKM.js";
import "./chunk-PKX2G6II.js";
import {
  install as install25,
  install10 as install34,
  install11 as install35,
  install14 as install39,
  install15 as install40,
  install16 as install41,
  install17 as install42,
  install18 as install43,
  install19 as install44,
  install2 as install26,
  install20 as install45,
  install21 as install46,
  install22 as install47,
  install3 as install27,
  install4 as install28,
  install5 as install29,
  install6 as install30,
  install7 as install31,
  install8 as install32,
  install9 as install33
} from "./chunk-DIU5CUFM.js";
import {
  install as install38
} from "./chunk-OORZMOXS.js";
import {
  install as install36
} from "./chunk-SAGDWLW7.js";
import {
  install as install37
} from "./chunk-6D5TN6IN.js";
import "./chunk-ZR4V7UZP.js";
import {
  install as install48
} from "./chunk-22WAPDAC.js";
import {
  install3 as install6,
  install4 as install13
} from "./chunk-FNFYBW3T.js";
import "./chunk-6MK55XBE.js";
import "./chunk-ZONBLEOS.js";
import "./chunk-QAR3K42R.js";
import {
  Axis_default,
  parseGeoJSON
} from "./chunk-BUGQH6DG.js";
import "./chunk-YB2SVXLR.js";
import {
  Chart_default,
  Component_default as Component_default2,
  PRIORITY,
  SeriesData_default,
  connect,
  dataTool,
  dependencies,
  disConnect,
  disconnect,
  dispose,
  getCoordinateSystemDimensions,
  getInstanceByDom,
  getInstanceById,
  getMap,
  init,
  registerAction,
  registerCoordinateSystem,
  registerLayout,
  registerLoading,
  registerMap,
  registerPostInit,
  registerPostUpdate,
  registerPreprocessor,
  registerProcessor,
  registerTheme,
  registerTransform,
  registerUpdateLifecycle,
  registerVisual,
  setCanvasCreator,
  throttle,
  use,
  version
} from "./chunk-H732WCN4.js";
import {
  Component_default,
  Model_default,
  Series_default,
  registerLocale
} from "./chunk-Q47K3BNQ.js";
import {
  brushSingle,
  zrender_exports
} from "./chunk-OUMNO3IT.js";
import {
  color_exports,
  env_default,
  matrix_exports,
  setPlatformAPI,
  util_exports,
  vector_exports
} from "./chunk-MUBQFVAI.js";
import "./chunk-GFT2G5UO.js";

// node_modules/.pnpm/echarts@5.5.1/node_modules/echarts/index.js
use([install50]);
use([install49]);
use([install, install2, install3, install4, install5, install7, install8, install9, install10, install11, install12, install14, install15, install16, install17, install18, install19, install20, install21, install22, install23, install24]);
use(install26);
use(install27);
use(install6);
use(install28);
use(install13);
use(install29);
use(install30);
use(install31);
use(install32);
use(install25);
use(install33);
use(install34);
use(install35);
use(install36);
use(install37);
use(install38);
use(install39);
use(install42);
use(install40);
use(install41);
use(install45);
use(install43);
use(install44);
use(install46);
use(install47);
use(install48);
use(installUniversalTransition);
use(installLabelLayout);
export {
  Axis_default as Axis,
  Chart_default as ChartView,
  Component_default as ComponentModel,
  Component_default2 as ComponentView,
  SeriesData_default as List,
  Model_default as Model,
  PRIORITY,
  Series_default as SeriesModel,
  color_exports as color,
  connect,
  dataTool,
  dependencies,
  disConnect,
  disconnect,
  dispose,
  env_default as env,
  extendChartView,
  extendComponentModel,
  extendComponentView,
  extendSeriesModel,
  format_exports as format,
  getCoordinateSystemDimensions,
  getInstanceByDom,
  getInstanceById,
  getMap,
  graphic_exports as graphic,
  helper_exports as helper,
  init,
  brushSingle as innerDrawElementOnCanvas,
  matrix_exports as matrix,
  number_exports as number,
  parseGeoJSON,
  parseGeoJSON as parseGeoJson,
  registerAction,
  registerCoordinateSystem,
  registerLayout,
  registerLoading,
  registerLocale,
  registerMap,
  registerPostInit,
  registerPostUpdate,
  registerPreprocessor,
  registerProcessor,
  registerTheme,
  registerTransform,
  registerUpdateLifecycle,
  registerVisual,
  setCanvasCreator,
  setPlatformAPI,
  throttle,
  time_exports as time,
  use,
  util_exports2 as util,
  vector_exports as vector,
  version,
  util_exports as zrUtil,
  zrender_exports as zrender
};
//# sourceMappingURL=echarts.js.map
