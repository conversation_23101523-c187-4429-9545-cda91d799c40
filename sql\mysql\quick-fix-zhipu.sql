-- =====================================================
-- 智谱AI快速修复脚本
-- 针对已有智谱AI配置的快速修复
-- =====================================================

-- 快速修复智谱AI URL配置
UPDATE ai_api_key 
SET url = 'https://open.bigmodel.cn/api/paas/v4/' 
WHERE platform = 'ZhiPu';

-- 快速修复智谱AI模型名称（改为小写）
UPDATE ai_model 
SET model = CASE 
    WHEN model = 'GLM-4.5' THEN 'glm-4.5'
    WHEN model = 'GLM-4' THEN 'glm-4'
    WHEN model = 'GLM-3-TURBO' THEN 'glm-3-turbo'
    WHEN model = 'GLM-3-Turbo' THEN 'glm-3-turbo'
    ELSE LOWER(model)
END
WHERE platform = 'ZhiPu';

-- 启用智谱AI配置（如果API密钥有效）
-- 注意：请确保API密钥是有效的再执行此步骤
-- UPDATE ai_api_key SET status = 0 WHERE platform = 'ZhiPu' AND api_key != 'your-zhipu-api-key-here';
-- UPDATE ai_model SET status = 0 WHERE platform = 'ZhiPu';

-- 验证修复结果
SELECT 'ZhiPu API Keys:' as type, id, name, url, status FROM ai_api_key WHERE platform = 'ZhiPu'
UNION ALL
SELECT 'ZhiPu Models:', id, name, model, status FROM ai_model WHERE platform = 'ZhiPu';
