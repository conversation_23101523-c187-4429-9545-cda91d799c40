# 智谱AI模型对话报错问题排查指南

## 问题现象
智谱AI模型在对话时报错，无法正常生成回复。

## 常见原因分析

### 1. 数据库配置问题
- **URL配置错误**：未使用OpenAI兼容模式的URL
- **模型名称格式错误**：使用了大写格式而非小写
- **状态配置错误**：API密钥或模型被禁用

### 2. API密钥问题
- **密钥无效**：API密钥已过期或格式不正确
- **账户余额不足**：智谱AI账户余额不足
- **权限问题**：API密钥权限不足

### 3. 网络连接问题
- **URL不可访问**：配置的URL无法访问
- **网络超时**：请求超时

## 排查步骤

### 步骤1：检查数据库配置

```sql
-- 检查智谱AI API密钥配置
SELECT id, name, platform, api_key, url, status 
FROM ai_api_key 
WHERE platform = 'ZhiPu';

-- 检查智谱AI模型配置
SELECT id, name, model, platform, status 
FROM ai_model 
WHERE platform = 'ZhiPu';
```

**正确配置应该是：**
- URL: `https://open.bigmodel.cn/api/paas/v4/`
- 模型名称: `glm-4.5`（小写）
- 状态: `0`（启用）

### 步骤2：修复数据库配置

执行修复脚本：
```bash
# 完整修复脚本
mysql -u root -p your_database < sql/mysql/fix-zhipu-ai-config.sql

# 或快速修复脚本
mysql -u root -p your_database < sql/mysql/quick-fix-zhipu.sql
```

### 步骤3：验证API密钥

1. **检查密钥格式**：
   - 正确格式：`32f84543e54eee31f8d56b2bd6020573.3vh9idLJZ2ZhxDEs`
   - 包含点号分隔的两部分

2. **登录智谱AI官网验证**：
   - 访问：https://open.bigmodel.cn
   - 检查API密钥是否有效
   - 确认账户余额

### 步骤4：使用智能配置功能

1. 进入系统管理 → AI模型管理 → API密钥管理
2. 找到智谱AI配置，点击编辑
3. 点击"使用默认"按钮自动配置URL
4. 点击"测试连接"验证配置
5. 保存配置

### 步骤5：检查应用日志

查看应用启动日志和错误日志：
```bash
# 查看应用日志
tail -f logs/spring.log

# 查看错误日志
grep -i "zhipu\|智谱" logs/spring.log
grep -i "error" logs/spring.log | grep -i "ai"
```

## 解决方案

### 方案1：数据库修复（推荐）

执行提供的SQL修复脚本，自动修复所有配置问题。

### 方案2：手动配置

1. **更新API密钥URL**：
```sql
UPDATE ai_api_key 
SET url = 'https://open.bigmodel.cn/api/paas/v4/' 
WHERE platform = 'ZhiPu';
```

2. **修正模型名称**：
```sql
UPDATE ai_model 
SET model = 'glm-4.5' 
WHERE platform = 'ZhiPu' AND model = 'GLM-4.5';
```

3. **启用配置**：
```sql
UPDATE ai_api_key SET status = 0 WHERE platform = 'ZhiPu';
UPDATE ai_model SET status = 0 WHERE platform = 'ZhiPu';
```

### 方案3：重新创建配置

如果修复无效，可以删除现有配置重新创建：

```sql
-- 删除现有配置
DELETE FROM ai_model WHERE platform = 'ZhiPu';
DELETE FROM ai_api_key WHERE platform = 'ZhiPu';

-- 然后执行完整修复脚本创建新配置
```

## 验证修复结果

### 1. 数据库验证
```sql
SELECT '智谱AI配置验证' as info;
SELECT a.name as api_name, a.url, a.status as api_status,
       m.name as model_name, m.model, m.status as model_status
FROM ai_api_key a
LEFT JOIN ai_model m ON a.id = m.key_id
WHERE a.platform = 'ZhiPu';
```

### 2. 功能验证
1. 进入AI聊天界面
2. 选择智谱AI模型
3. 发送测试消息
4. 检查是否正常回复

## 预防措施

1. **定期检查配置**：定期验证AI平台配置的有效性
2. **监控账户余额**：确保各AI平台账户余额充足
3. **使用智能配置**：优先使用新的智能配置功能
4. **备份配置**：定期备份AI配置数据

## 技术说明

### 智谱AI OpenAI兼容模式
智谱AI官方支持OpenAI SDK兼容模式，这样可以：
- 解决流式响应的content-type问题
- 统一API调用方式
- 提高兼容性和稳定性

### URL配置说明
- **官方URL**: `https://open.bigmodel.cn/api/paas/v4/`
- **兼容格式**: OpenAI API格式
- **支持功能**: 聊天、流式响应、工具调用

### 模型名称规范
- 使用小写格式：`glm-4.5`、`glm-4`
- 避免大写格式：`GLM-4.5`、`GLM-4`
- 保持与官方文档一致

## 联系支持

如果问题仍然存在，请：
1. 收集完整的错误日志
2. 提供数据库配置截图
3. 联系技术支持团队
